#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quote/0 API 图形界面客户端
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import requests
import base64
import json
import threading
from pathlib import Path
import os


class Quote0GUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Quote/0 消息发送器")
        self.root.geometry("600x500")
        self.root.resizable(True, True)

        # 配置参数
        self.api_key = "dot_app_hBpJUdQwxjGqdoiTFsYTPEondtDsSGKECsHQJDlfIbefVVYajmxuJLlQiyxsVHiS"
        self.device_id = "E4B063CC5A74"
        self.icon_path = None

        self.setup_ui()

    def setup_ui(self):
        # 主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # 标题
        title_label = ttk.Label(main_frame, text="Quote/0 消息发送器", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # 标题输入框 - 使用Text控件增加高度
        ttk.Label(main_frame, text="标题:").grid(row=3, column=0, sticky=(tk.W, tk.N), pady=5)
        self.title_text = tk.Text(main_frame, height=2, width=50, wrap=tk.WORD)
        self.title_text.insert(tk.END, "")
        self.title_text.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=5)

        # 标题滚动条
        title_scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=self.title_text.yview)
        title_scrollbar.grid(row=3, column=2, sticky=(tk.N, tk.S), pady=5)
        self.title_text.configure(yscrollcommand=title_scrollbar.set)

        # 消息内容
        ttk.Label(main_frame, text="消息内容:").grid(row=4, column=0, sticky=(tk.W, tk.N), pady=5)
        self.message_text = tk.Text(main_frame, height=6, width=50, wrap=tk.WORD)
        self.message_text.insert(tk.END, "")
        self.message_text.grid(row=4, column=1, sticky=(tk.W, tk.E), pady=5)

        # 消息滚动条
        scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=self.message_text.yview)
        scrollbar.grid(row=4, column=2, sticky=(tk.N, tk.S), pady=5)
        self.message_text.configure(yscrollcommand=scrollbar.set)

        # 发送按钮
        self.send_button = ttk.Button(main_frame, text="发送消息", command=self.send_message_thread)
        self.send_button.grid(row=8, column=0, columnspan=2, pady=20)

        # 状态显示
        self.status_var = tk.StringVar(value="就绪")
        status_label = ttk.Label(main_frame, textvariable=self.status_var)
        status_label.grid(row=9, column=0, columnspan=2, pady=5)

        # 日志显示
        ttk.Label(main_frame, text="日志:").grid(row=10, column=0, sticky=(tk.W, tk.N), pady=5)
        self.log_text = tk.Text(main_frame, height=8, width=50, wrap=tk.WORD, state=tk.DISABLED)
        self.log_text.grid(row=10, column=1, sticky=(tk.W, tk.E), pady=5)

        log_scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        log_scrollbar.grid(row=10, column=2, sticky=(tk.N, tk.S), pady=5)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

    def select_icon(self):
        """选择图标文件"""
        file_path = filedialog.askopenfilename(
            title="选择图标文件",
            filetypes=[("图片文件", "*.png *.jpg *.jpeg *.gif *.bmp"), ("所有文件", "*.*")]
        )
        if file_path:
            self.icon_path = file_path
            self.icon_label.config(text=Path(file_path).name)

    def clear_icon(self):
        """清除图标"""
        self.icon_path = None
        self.icon_label.config(text="未选择")

    def log_message(self, message):
        """添加日志消息"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)

    def encode_image_to_base64(self, image_path):
        """将图片文件编码为base64格式"""
        try:
            with open(image_path, 'rb') as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            self.log_message(f"图标文件读取失败: {e}")
            return None

    def send_message_thread(self):
        """在新线程中发送消息"""

        def send():
            self.send_button.config(state=tk.DISABLED)
            self.status_var.set("发送中...")

            try:
                self.send_message()
            finally:
                self.send_button.config(state=tk.NORMAL)
                self.status_var.set("就绪")

        threading.Thread(target=send, daemon=True).start()

    def send_message(self):
        """发送消息到设备"""
        # 获取输入值 - 修复变量引用
        api_key = self.api_key
        device_id = self.device_id
        title = self.title_text.get("1.0", tk.END).strip()
        message = self.message_text.get("1.0", tk.END).strip()

        # 验证必填字段
        if not all([api_key, device_id, title, message]):
            messagebox.showerror("错误", "请填写所有必填字段（标题、消息内容）")
            return

        # API地址
        url = "https://dot.mindreset.tech/api/open/text"

        # 请求头
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json',
            'User-Agent': 'Quote0-Python-Client/1.0'
        }

        # 构建请求数据
        payload = {
            "deviceId": device_id,
            "title": title,
            "message": message
        }

        # 添加签名（使用默认值）
        payload["signature"] = "zhaodsh"

        if self.icon_path:
            icon_base64 = self.encode_image_to_base64(self.icon_path)
            if icon_base64:
                payload["icon"] = icon_base64
                self.log_message(f"✓ 成功加载图标: {Path(self.icon_path).name}")

        # 显示发送信息
        self.log_message("正在发送消息...")
        self.log_message(f"设备ID: {device_id}")
        self.log_message(f"标题: {title}")
        self.log_message(f"消息: {message}")
        self.log_message(f"签名: zhaodsh")
        self.log_message("-" * 40)

        try:
            # 发送请求
            response = requests.post(url, headers=headers, json=payload, timeout=30, verify=False,
                                     proxies={"http": None, "https": None})

            # 显示结果
            self.log_message(f"状态码: {response.status_code}")

            if response.ok:
                self.log_message("✓ 消息发送成功！")
                messagebox.showinfo("成功", "消息发送成功！")
            else:
                self.log_message("✗ 消息发送失败！")
                messagebox.showerror("失败", "消息发送失败！")

            # 显示响应内容
            try:
                response_data = response.json()
                self.log_message("响应内容:")
                self.log_message(json.dumps(response_data, indent=2, ensure_ascii=False))
            except:
                self.log_message("响应内容:")
                self.log_message(response.text)

        except requests.exceptions.Timeout:
            error_msg = "请求超时，请检查网络连接"
            self.log_message(f"✗ {error_msg}")
            messagebox.showerror("错误", error_msg)
        except requests.exceptions.ConnectionError:
            error_msg = "连接失败，请检查网络或API地址"
            self.log_message(f"✗ {error_msg}")
            messagebox.showerror("错误", error_msg)
        except Exception as e:
            error_msg = f"发送失败: {e}"
            self.log_message(f"✗ {error_msg}")
            messagebox.showerror("错误", error_msg)


def main():
    root = tk.Tk()
    app = Quote0GUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()