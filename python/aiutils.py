# 导入模块
import requests
from lxml import etree
import json
import requests

import warnings
import whisper
from pydub import AudioSegment
import os
import ffmpeg
from PIL import Image, ImageDraw, ImageFont
import sys

from pymongo import MongoClient

# 连接MongoDB
client = MongoClient('mongodb://************:27097/')
# 选择数据库
db = client['wordstudy']

c_dailynews = db['dailynews']


import socket

os.environ['OPENAI_API_KEY'] = 'sk-zk26720dccc28d5150ad37fee3d886afdef65d741242b193'
import openai
# 获取电脑名称
computer_name = socket.gethostname()

print(computer_name)

if computer_name == 'tian':
    os.environ["http_proxy"] = "http://127.0.0.1:7890"
    os.environ["https_proxy"] = "http://127.0.0.1:7890"
elif computer_name == 'zhaodsh-pc':
    os.environ["http_proxy"] = "http://127.0.0.1:7890"
    os.environ["https_proxy"] = "http://127.0.0.1:7890"
else:
    pass


def ask(input_text):
    client = openai.OpenAI(
        # api_key='sk-zk26720dccc28d5150ad37fee3d886afdef65d741242b193',
        # base_url="https://api.zhizengzeng.com/v1",
        api_key='0a2d2d62-2530-47b3-b8d5-a40a153acc4b',
        base_url='https://ark.cn-beijing.volces.com/api/v3'
    )

    model = 'chatgpt-4o-latest'
    # model = 'claude-3-7-sonnet-20250219'
    model = 'deepseek-v3-241226'
    print('使用模型：' + model)

    # 使用 GPT-3.5 模型进行文本总结
    response = client.chat.completions.create(
        messages=[
            {
                "role": "user",
                "content": input_text,
            }
        ],
        model=model,
        temperature=0.75  # 调整温度，控制生成文本的创造性
    )

    # 提取生成的总结概要
    summary = response.choices[0].message.content
    return summary


def trans_news(words, news):

    prompt = f'''
    您将接收到一个中文新闻的数组几个待练习的目标单词。请先阅读全部新闻,然后按以下要求将新闻翻译成简单的美式英语新闻：

    1. 目标单词要求：
        1.1 必须使用所有给定的目标单词，一个不能少
        1.2 翻译前，先列出每个目标单词将用在哪条新闻中的规划表
        1.3 每条新闻至少使用2-3个目标单词
        1.4 每个单词使用后,在规划表中标记已用
        1.5 翻译完成后，复查确保所有单词都已使用
        1.6 可灵活使用单词的各种形式(动词变位、名词单复数等)
        1.7 单词使用必须自然融入上下文
        1.8 优先在主题相关的新闻中使用对应单词


    2.语言难度控制：
        2.1 除了目标单词外，90%使用前2000个最常用英语单词
        2.2 除了目标单词外，其余单词不超过前2000个最常用词
        2.3 专业术语改用简单词组解释
        2.4 8-10岁美国儿童可以理解
        
    3. 句式要求：
        3.1 每句不超过15个单词
        3.2 每条新闻最多5个句子
        3.3 以简单句为主,避免复杂从句
        3.4 使用主动语态
    4. 内容处理：
        4.1 保留原文核心信息和要点，尤其是数字信息
        4.2 使用美式新闻写作风格
        4.4 适当用代词避免重复
        4.5 调整语序使表达更自然
        4.6 每条新闻都要输出，即使没有使用目标单词
        
    5. 质量检查要求：
        5.1. 确保每条新闻翻译后意思准确完整
        5.2. 确保所有目标单词使用自然,不生硬
        5.3. 确保没有遗漏任何目标单词
        5.4. 确保语言难度符合要求
        5.5. 确保句式符合要求
        
    目标单词列表：
    {words}

    新闻列表：
    {news}


    输出格式要求：
    1. 先列出目标单词使用规划表:
       新闻1: [计划使用的单词...]
       新闻2: [计划使用的单词...]
       新闻3: [无目标单词...]
       ...
    
    2. 然后输出翻译的新闻:
       1. [翻译内容]
       2. [翻译内容]
       3. [翻译内容]
       ...
    
    3. 最后列出单词使用核查表:
       word1: ✓ (用于新闻X)
       word2: ✓ (用于新闻Y)
       ...
       
    严格按照下面例子格式输出，不要增加任何和markdown有关的标识符：
    1. 单词使用规划表
    新闻1: [hairdresser, idle, jealous]
    新闻2: [identification, labour, pension]
    新闻3: [identify, magnify, overview]
    新闻4: [idiom, jar, lack, naive]
    新闻5: []
    
    2. 翻译新闻
    1.xxxx
    2.xxx
    3.xxx
    4.xxx
    5.xxx
    
    3. 单词使用核查表
    hairdresser: ✓ (用于新闻1)
    identification: ✓ (用于新闻2)
    identify: ✓ (用于新闻3)
    idiom: ✓ (用于新闻4)
    idle: ✓ (用于新闻1)
    jar: ✓ (用于新闻4)
    jealous: ✓ (用于新闻1)
    labour: ✓ (用于新闻2)
    lack: ✓ (用于新闻4)
    magnify: ✓ (用于新闻3)
    naive: ✓ (用于新闻4)
    overview: ✓ (用于新闻3)
    pension: ✓ (用于新闻2)
    '''

    return ask(prompt)





words = [
    "hairdresser",
    "identification",
    "identify",
    "idiom",
    "idle",
    "jar",
    "jealous",
    "labour",
    "lack",
    "magnify",
    "naive",
    "overview",
    "pension"
]
news = ["1. 气象台：我国中东部气温将自北向南先后下降6～10℃，局地降温18℃以上。", "2. 司法部：今日起，在全国范围内全面推行在线行政复议。", "3. 全国高度近视患者超1.3亿人，委员建议将高度近视纳入慢病管理。", "4. 人大代表建议：将元宵节设为国家法定节假日。", "5. 7年来首次反弹！2024年我国出生人口954万，较前年增加52万。", "6. 中国“祝融号”发现火星中低纬度地区曾存在古代海洋，证实火星曾经宜居。", "7. 北京：3月3日起人工耳蜗集采产品纳入医保报销。", "8. 上海中小学全面实施AB制午餐模式：提供两种餐食方案供学生选择。", "9. 山西提出：今年建成50个乡镇级和650个村级标准化饮用水源地，消除饮用水源环境污染隐患。", "10. 3月3日起黄山、花山谜窟、太平湖三大景区对女游客免门票7天。", "11. 10年间拐卖17名儿童，人贩子余华英已被执行死刑。", "12. 中国与巴基斯坦签署选拔训练航天员合作协议，中国空间站将迎来首位外籍航天员造访。", "13. 调结果显示，韩国近六成民众支持罢免总统尹锡悦。", "14. 法国官员称：法国也想要乌克兰的矿产资源，用作武器原材料。", "15. 特朗普计划签署行政令，将首次确定英语为美国官方语言。"]


import time
start_time = time.time()
ret = trans_news(words, news)
end_time = time.time()
print(f"Translation took {end_time - start_time:.2f} seconds")
print(ret)